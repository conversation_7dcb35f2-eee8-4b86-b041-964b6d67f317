html {
  scroll-behavior: smooth;
  scroll-padding-top: 5rem; /* Adjust this value based on your sticky header's height */
}
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .bg-page {
    background-color: var(--aw-color-bg-page);
  }
  .bg-dark {
    background-color: var(--aw-color-bg-page-dark);
  }
  .bg-light {
    background-color: var(--aw-color-bg-page);
  }
  .bg-gradient {
    background-color: transparent;
    background-image: linear-gradient(180deg, #00000000 0%, #08080800 27%);
    opacity: 1;
    transition:
      background 0.3s,
      border-radius 0.3s,
      opacity 0.3s;
  }
  .text-page {
    color: var(--aw-color-text-page);
  }
  .text-muted {
    color: var(--aw-color-text-muted);
  }
  .text-shadow {
    text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.99);
  }
  .box-shadow {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.5);
  }
  .text-title {
    @apply text-[#65bee6] font-[Raleway] text-xl font-medium uppercase italic leading-[1em] tracking-[1.3px];
  }
  .text-title-small {
    @apply text-[#65bee6] text-xs leading-3 italic;
  }
  .text-subtitle {
    @apply mb-5 text-white text-[40px] font-medium capitalize leading-[1.25em];
  }
  .text-subtitle-small {
    @apply text-2xl leading-8;
  }
  .text-tagline,
  .text-content {
    @apply mb-7 text-white font-[Raleway] text-base font-light;
  }
  .text-tagline-small,
  .text-content-small {
    @apply text-sm font-light leading-5 tracking-normal;
  }
  .text-hero,
  .text-cta {
    @apply font-['Times_New_Roman'] text-white font-medium uppercase text-shadow text-[40px] leading-[1.1em];
  }
  .text-hero-lg,
  .text-cta-large {
    @apply text-[78px];
  }
  .text-hero-content {
    @apply text-sm font-light leading-5 tracking-normal;
  }
  .text-hero-content-lg {
    @apply text-[16px] font-medium tracking-wide  leading-[44px] text-white;
  }
  .text-card-title {
    @apply uppercase font-['Raleway_Variable'] font-medium text-xs  leading-3 tracking-[1.3px];
  }
}

@layer components {
  .btn {
    @apply min-w-[170px] inline-flex items-center justify-center border-gray-400 border bg-gray-200 text-black font-['Times_New_Roman',sans-serif] text-xs leading-3 uppercase rounded-[3px] shadow-lg py-4 px-7  ease-in duration-200 cursor-pointer;
  }

  .btn-primary {
    @apply btn font-medium bg-primary text-white dark:text-black dark:bg-[#E9E9E9]  hover:scale-110;
  }

  .btn-secondary {
    @apply btn leading-4 text-white fill-white bg-white/10 border-white/30 border-2 rounded-[3px] backdrop-blur-sm px-[15px] py-[15px] hover:scale-110 hover:bg-white/20 hover:border-white/50 transition-all;
  }

  .btn-tertiary {
    @apply btn border-none shadow-none text-muted hover:text-gray-900 dark:text-gray-400 dark:hover:text-white;
  }
}

#header.scroll > div:first-child {
  @apply bg-page md:bg-white/90 md:backdrop-blur-md;
  box-shadow: 0 0.375rem 1.5rem 0 rgb(140 152 164 / 13%);
}
.dark #header.scroll > div:first-child,
#header.scroll.dark > div:first-child {
  @apply bg-page md:bg-black/[.75] md:backdrop-blur-lg;
  box-shadow: none;
}
/* #header.scroll > div:last-child {
  @apply py-3;
} */

#header.expanded nav {
  position: fixed;
  top: 100px;
  left: 0;
  right: 0;
  bottom: 70px !important;
  padding: 0 5px;
}
#header.expanded nav li a {
  width: auto;
  padding: 25px 25px 20px 25px;
}

.dropdown:hover .dropdown-menu {
  display: block;
}

[astro-icon].icon-light > * {
  stroke-width: 1.2;
}

[astro-icon].icon-bold > * {
  stroke-width: 2.4;
}

[data-aw-toggle-menu] path {
  @apply transition;
}
[data-aw-toggle-menu].expanded g > path:first-child {
  @apply -rotate-45 translate-y-[15px] translate-x-[-3px];
}

[data-aw-toggle-menu].expanded g > path:last-child {
  @apply rotate-45 translate-y-[-8px] translate-x-[14px];
}

/* @media (min-width: 768px) {
  .fleet-group > :nth-child(3n) {
    grid-column: span 2;
  }
}
 @media (min-width: 1024px) {
  .fleet-group > :nth-child(3n) {
    grid-column: span 1;
  }
} */
/* To deprecated */

.dd *:first-child {
  margin-top: 0;
}
