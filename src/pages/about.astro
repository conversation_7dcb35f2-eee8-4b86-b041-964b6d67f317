---
import { ClientRouter } from 'astro:transitions';
import Content from '~/components/widgets/Content.astro';
import Layout from '~/layouts/PageLayout.astro';
import { getPermalink } from '~/utils/permalinks';
import { fade } from 'astro:transitions';
import { Image } from 'astro:assets';
import photo1 from 'src/assets/images/radley_chelli.jpg';
const metadata = {
  title: 'About us',
};
---

<Layout metadata={metadata}>
  <ClientRouter />
  
  <!-- Hero Section with Image and Introduction -->
  <Content 
    transition:animate={fade({ duration: '0.5s' })}
    isReversed={false}
    image={{
      src: photo1,
      alt: '<PERSON><PERSON> and <PERSON><PERSON><PERSON>',
      width: 500,
      height: 500,
      class: "hover:scale-105 transition-transform duration-500 rounded-lg shadow-lg translate-y-4"
    }}
    id="about-hero"
  >
    <Fragment slot="title">
      <div class="translate-x-full intersect:translate-x-0 transition duration-1000 intersect-once">
        <p class="text-[#65bee6] font-[Raleway] text-xl font-medium uppercase italic leading-[1em] tracking-[1.3px]">About Us</p>
      </div>
    </Fragment>
    <Fragment slot="content">
      <div class="space-y-4">
        <h2 class="text-lg md:text-xl font-medium mb-8 -translate-x-full intersect:translate-x-0 transition duration-1000 intersect-once">
          At Rad Transport, We Deliver 
          <span class="relative">
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600">Unparalleled Luxury</span>
            <span class="absolute -bottom-1 left-0 w-full h-1 bg-primary/30 rounded-full"></span>
          </span> 
          and Elite Travel Experiences
        </h2>
        
        <div class="translate-y-12 opacity-0 intersect:translate-y-0 intersect:opacity-100 transition duration-1000 intersect-once mt-10">
          <p class="font-medium text-lg mb-3">Meet Radley Dougherty</p>
          <p class="relative pl-4 border-l-2 border-primary hover:border-l-4 transition-all duration-300">
            I'm a dynamic blend of science, sales, and a relentless pursuit of passion. After earning my Bachelor of Science in Molecular and Microbiology from the University of Central Florida, my journey took me through nearly every kind of sales role imaginable—inside sales, door-to-door, business-to-business, and medical device sales.
          </p>
        </div>
        
        <p class="opacity-0 intersect:opacity-100 transition duration-1000 delay-300 intersect-once">
          My career reached a pinnacle during my seven years with Abbott Vascular, where I was honored with accolades like Field Associate of the Year and Presidents Club. But success in the corporate world wasn't the final chapter of my story.
        </p>
        
        <div class="flex gap-3 pt-4 opacity-0 intersect:opacity-100 transition duration-1000 delay-500 intersect-once">
          <a href="#mission" class="btn btn-primary mission-btn flex items-center gap-1 uppercase font-semibold tracking-wide text-sm">
            Our Mission
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="arrow-icon">
              <path d="m6 9 6 6 6-6"/>
            </svg>
          </a>
          
          <a href="https://book.mylimobiz.com/v4/radtransport" class="btn btn-secondary uppercase font-semibold tracking-wide text-sm">
            Book Now
          </a>
        </div>
      </div>
    </Fragment>
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent bg-gradient"></div>
    </Fragment>
  </Content>
  
  <!-- Career Journey Section -->
  <Content transition:animate="fade" class="pt-0 md:pt-4">
    <Fragment slot="content">
      <div class="space-y-4 opacity-0 intersect:opacity-100 transition duration-1000 intersect-once">
        <p>
          I felt the pull of the mountains, snowboarding, and a deep connection to the outdoors. That call inspired a leap of faith—combining my business acumen and love for adventure to solve a persistent problem I saw in the Four Corners community: transportation.
        </p>
        <p>
          Today, I'm proud to lead a business that helps others gain access to the mountains and the outdoors with ease. Together with my fiancée, Rucheli—who seamlessly balances being my partner in life and in business—we're building something that's more than a company; it's a way to bring people closer to the adventures that inspire them.
        </p>
      </div>
    </Fragment>
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50/30 dark:bg-transparent"></div>
    </Fragment>
  </Content>
  
  <!-- Mission and Philosophy Sections -->
  <Content 
    isReversed={true}
    transition:animate="fade"
    id="mission"
  >
    <Fragment slot="title">
      <p class="text-[#65bee6] font-[Raleway] text-xl font-medium uppercase italic leading-[1em] tracking-[1.3px] translate-x-full intersect:translate-x-0 transition duration-1000 intersect-once">Our Mission</p>
    </Fragment>
    <Fragment slot="content">
      <div class="bg-blue-50/70 dark:bg-slate-800/50 p-6 rounded-lg shadow-sm opacity-0 intersect:opacity-100 transition duration-1000 delay-200 intersect-once">
        <p class="text-lg italic">
          "Empowering people to connect with nature, chase their passions, and experience the outdoors without limits."
        </p>
      </div>
    </Fragment>
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-gradient-to-b from-blue-50/50 to-white dark:from-transparent dark:to-transparent"></div>
    </Fragment>
  </Content>
  
  <Content transition:animate="fade">
    <Fragment slot="title">
      <p class="text-[#65bee6] font-[Raleway] text-xl font-medium uppercase italic leading-[1em] tracking-[1.3px] -translate-x-full intersect:translate-x-0 transition duration-1000 intersect-once">Our Philosophy</p>
    </Fragment>
    <Fragment slot="content">
      <div class="bg-blue-50/70 dark:bg-slate-800/50 p-6 rounded-lg shadow-sm opacity-0 intersect:opacity-100 transition duration-1000 delay-200 intersect-once">
        <p class="text-lg italic">
          "Life is an adventure shaped by bold moves and meaningful connections. Success is measured not just by what you achieve, but by the impact you leave on the world around you."
        </p>
      </div>
    </Fragment>
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-gradient-to-t from-blue-50/50 to-white dark:from-transparent dark:to-transparent"></div>
    </Fragment>
  </Content>
  
  <!-- Call to Action -->
  <Content
    callToAction={{
      variant: 'primary',
      text: 'Book Your Adventure Today',
      href: "https://book.mylimobiz.com/v4/radtransport",
      classes: 'uppercase font-semibold tracking-wide text-sm'
    }}
    transition:animate="fade"
  >
    <Fragment slot="title">
      <p class="text-lg text-center translate-y-12 opacity-0 intersect:translate-y-0 intersect:opacity-100 transition duration-1000 intersect-once">Ready to Experience the Mountains?</p>
    </Fragment>
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Content>
</Layout>

<script>
  // Smooth scroll for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', (e) => {
      e.preventDefault();
      const href = anchor.getAttribute('href');
      if (href) {
        const target = document.querySelector(href) as HTMLElement;
        if (target) {
          window.scrollTo({
            top: target.offsetTop - 100,
            behavior: 'smooth'
          });
        }
      }
    });
  });
</script>

<style>
  @keyframes arrow-bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(4px);
    }
  }
  
  @keyframes arrow-pulse {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(6px);
    }
  }
  
  .arrow-icon {
    animation: arrow-bounce 2s ease infinite;
  }
  
  .mission-btn:hover .arrow-icon {
    animation: arrow-pulse 1.2s ease infinite;
  }
</style>
