---
import { ClientRouter } from 'astro:transitions';
import Layout from '~/layouts/PageLayout.astro';

import Hero from '~/components/widgets/Hero.astro';
import Note from '~/components/widgets/Note.astro';
import Features from '~/components/widgets/Features.astro';
//import Features2 from '~/components/widgets/Features2.astro';
//import Steps from '~/components/widgets/Steps.astro';
import Content from '~/components/widgets/Content.astro';
//import BlogLatestPosts from '~/components/widgets/BlogLatestPosts.astro';
//import FAQs from '~/components/widgets/FAQs.astro';
//import Stats from '~/components/widgets/Stats.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import Fleet from '~/components/widgets/Fleet.astro';
//import Features2 from '~/components/widgets/Features2.astro';
import gmcImage from 'src/assets/images/2021_gmc_yukon.png';
import { getPermalink } from '~/utils/permalinks';

const metadata = {
  title: 'Rad Transport',
  ignoreTitleTemplate: true,
};
---

<Layout metadata={metadata}>
  <ClientRouter />
  <!-- Hero Widget ******************* -->

  <Hero
    isDark
    actions={[
      {
        variant: 'secondary',
        text: 'Book Now',
        href: "https://book.mylimobiz.com/v4/radtransport",
      },
    ]}
    videoSrc="https://res.cloudinary.com/dm98joozq/video/upload/f_auto:video,q_auto/sqxkbnvdidgae3rywrbg"
  >
    <Fragment slot="title">Luxury Car Service</Fragment>

    <Fragment slot="subtitle">
      <span class="sm:block"> The Best Luxury Car Service in Colorado</span>
    </Fragment>
    <Fragment slot="content">
      <span class="block uppercase"> Rain or snow, we get you where you wanna go.</span>
    </Fragment>
  </Hero>

  <Content
    tagline="Luxury Travel Experience"
    title="At Rad Transport, We Deliver Unparalleled Luxury and Elite Travel Experiences"
    image={{
      src: gmcImage,
      alt: ' GMC Yukon',
    }}
    callToAction={{
      variant: 'primary',
      text: 'Call Now',
      href: 'tel:+***********',
    }}
    classes={{
      container: 'px-4 sm:px-6 lg:px-8'
    }}
  >
    <Fragment slot="content">
      Explore Colorado's stunning destinations with Rad Transport. Based in the heart of
      premier ski areas, we offer luxurious transportation for effortless access to
      Durango, Telluride, Montrose, and Silverton. Experience seamless travel to the
      slopes, regardless of the weather, with our commitment to comfort and reliability.
      'Rain or snow, we get you where you wanna go'
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent bg-gradient"></div>
    </Fragment>
  </Content>
  <div class="hidden lg:block">
    <Hero
      videoSrc="https://res.cloudinary.com/dm98joozq/video/upload/f_auto:video,q_auto/v8oqxboxqfrkrcefqfko"
    />
  </div>
  <Features
    title="Our Services"
    subtitle="Rain or snow, we get you where you wanna go."
    classes={{
      container: 'px-4 sm:px-6 lg:px-8'
    }}
    items={[
      {
        title: 'Airport Transfers',
        description:
          'Start your journey with a touch of luxury. Our airport transfer service ensures a seamless transition from the airport to your destination.',
        icon: 'tabler:plane-inflight',
      },
      {
        title: 'Ski Resort Transfers',
        description:
          'Travel to Colorado’s premier ski resorts in style. Our ski resort transfer service ensures a comfortable and convenient journey to the slopes.',
        icon: 'tabler:aerial-lift',
      },
      {
        title: 'Corporate Travel',
        description:
          'Impress your clients and colleagues with our corporate travel service. We provide professional and reliable transportation for all your business needs.',
        icon: 'tabler:briefcase',
      },
      {
        title: 'Special Events',
        description:
          'Make your special event even more memorable with our luxury transportation service. We provide elegant and stylish transportation for weddings, parties, and more.',
        icon: 'tabler:star',
      },
      {
        title: 'Hourly Charters',
        description:
          'Enjoy the flexibility of our hourly charter service. Whether you need transportation for a few hours or the whole day, we have you covered.',
        icon: 'tabler:clock',
      },
      {
        title: 'Customized Tours',
        description:
          'Experience the beauty of Colorado with our customized tour service. We offer personalized tours to suit your interests and preferences.',
        icon: 'tabler:map-2',
      },
      {
        title: 'Concierge Services',
        description:
          'Concierge services are available to assist with any special requests or arrangements you may need during your stay in Colorado.',
        icon: 'tabler:24-hours',
      },
      {
        title: 'Bike Shuttle',
        description:
          'We offer bike shuttle services to help you get to the best trails in Colorado. Our bike shuttle service is perfect for mountain bikers looking to explore the area.',
        icon: 'tabler:bike',
      },
    ]}
  />

  <!-- <Features2
    title="Most used widgets"
    subtitle="Provides frequently used components for building websites using Tailwind CSS"
    tagline="Components"
    items={[
      {
        title: 'Headers',
        description: "Ever tried driving without GPS? Boom! That's why websites need headers for direction.",
        icon: 'flat-color-icons:template',
      },
      {
        title: 'Heros',
        description:
          "Picture a superhero landing – epic, right? That's the job of a Hero section, making grand entrances!",
        icon: 'flat-color-icons:gallery',
      },
      {
        title: 'Features',
        description:
          'Where websites strut their stuff and show off superpowers. No holding back on the bragging rights here!',
        icon: 'flat-color-icons:approval',
      },
      {
        title: 'Content',
        description:
          "Dive into the meat and potatoes of a site; without it, you'd just be window shopping. Content is king.",
        icon: 'flat-color-icons:document',
      },
      {
        title: 'Call-to-Action',
        description:
          'That enthusiastic friend who\'s always urging, "Do it! Do it!"? Yeah, that\'s this button nudging you towards adventure.',
        icon: 'flat-color-icons:advertising',
      },
      {
        title: 'Pricing',
        description: 'Behold the dessert menu of the website world. Tempting choices await, can you resist?',
        icon: 'flat-color-icons:currency-exchange',
      },
      {
        title: 'Testimonial',
        description: 'Step into the gossip corner! Here, other visitors spill the beans and share the juicy details.',
        icon: 'flat-color-icons:voice-presentation',
      },
      {
        title: 'Contact',
        description:
          'Like a digital mailbox, but faster! Drop a line, ask a question, or send a virtual high-five. Ding! Message in.',
        icon: 'flat-color-icons:business-contact',
      },
      {
        title: 'Footers',
        description: "The footer's like the credits of a movie but sprinkled with easter eggs. Time to hunt!",
        icon: 'flat-color-icons:database',
      },
    ]}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent"></div>
    </Fragment>
  </Features2> -->
  <CallToAction
    actions={[
      {
        variant: 'primary',
        text: 'Call Now',
        href: 'tel:+***********',
        target: '_blank',
      },
    ]}
    classes={{
      container: 'px-4 sm:px-6 lg:px-8'
    }}
  >
    <Fragment slot="title">
      <p class="text-title-small md:text-title">OUR PHOTOS</p>
    </Fragment>

    <Fragment slot="subtitle">
      <p class="text-subtitle-small md:text-subtitle line-clamp-2">
        Premiere Car Service in Colorado.
      </p>
    </Fragment>
    <Fragment slot="tagline">
      <p class="text-tagline-small md:text-tagline">
        Unveil the splendor of Colorado with our premier black car service. Our fleet,
        featuring sophisticated luxury vehicles, caters to your desire for elegance and
        comfort. Journey through Colorado's breathtaking landscapes in unparalleled style
        and luxury, making every trip an indulgent experience.
      </p>
    </Fragment>
  </CallToAction>
  <Fleet />
  <Content
    isAfterContent
    columns="1"
    alignment="center"
    title="Premium Travel with Rad Transport"
    callToAction={[
      {
        variant: 'primary',
        text: 'Book Now',
        href: "https://book.mylimobiz.com/v4/radtransport",
      },
      {
        variant: 'secondary',
        text: 'More Info',
        href: '/about',
      },
    ]}
    classes={{
      container: 'px-4 sm:px-6 lg:px-8'
    }}
  >
    <Fragment slot="content">
      Experience unparalleled luxury with Rad Transport, where each journey through
      Colorado's winter wonderland becomes a distinguished adventure. We specialize in
      connecting you to the heart of Colorado's ski resorts, ensuring every ride is as
      seamless as the snow-capped mountains. Our motto, "Rain or snow, we get you where
      you wanna go" embodies our dedication to reliability and comfort, turning travel
      into an exclusive experience. Choose Rad Transport for your next ski trip and
      elevate your travel to a level of sophistication and enjoyment unmatched in the
      industry.
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-transparent bg-gradient"></div>
    </Fragment>
  </Content>

  <Note />
</Layout>
