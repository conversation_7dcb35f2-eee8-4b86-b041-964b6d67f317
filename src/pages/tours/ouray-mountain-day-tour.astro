---
import Layout from '../../layouts/PageLayout.astro';
import { ourayMountainDayTour as tour } from '../../data/tourData.js';
import Image from '../../components/common/Image.astro';
import Button from '../../components/ui/Button.astro';
import Headline from '../../components/ui/Headline.astro';
import Icon from '../../components/common/IconWrapper.astro';

const metadata = {
  title: tour.meta.title,
  description: tour.meta.description,
  canonical: Astro.url.href,
};
---

<Layout metadata={metadata}>


  <section slot="hero" class="relative w-screen h-[65vh] md:h-[75vh] flex items-center justify-center text-center text-white">
    <Image
      src={tour.hero.backgroundImage}
      alt="Panoramic view of San Juan Mountains"
      class="absolute inset-0 w-full h-full object-cover object-top -z-10"
      widths={[400, 768, 1024, 2048, 2560]}
      sizes="(max-width: 767px) 100vw, 100vw"
      width={2560}
      height={1396}
      loading="eager"
      decoding="async"
    />
    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-black/10 -z-10"></div>
    <div class="relative z-10 p-4">
      <Headline tag="h1" class="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight text-shadow-lg">
        {tour.hero.title}
      </Headline>
      <p class="text-lg md:text-xl lg:text-2xl mb-8 max-w-3xl mx-auto text-shadow-lg">{tour.hero.tagline}</p>
      <Button variant="primary" size="xl" href={tour.hero.ctaBook.href} target="_blank" rel="noopener noreferrer" class="transform hover:scale-105 transition-transform duration-200">
        {tour.hero.ctaBook.text}
      </Button>
    </div>
  </section>

  {/* Main page content - default slot */}
  <div class="bg-black text-white py-12 md:py-16">
    <div class="container mx-auto px-4 space-y-16 md:space-y-24">

      <!-- Combined Key Details & Overview Section -->
      <section>
        <div class="max-w-6xl mx-auto grid md:grid-cols-5 gap-8 md:gap-12 items-start">
          <div class="md:col-span-3">
            <Headline tag="h2" class="text-3xl md:text-4xl font-bold mb-4 text-primary">
              {tour.overview.title}
            </Headline>
            <p class="text-lg text-slate-300 leading-relaxed">
              {tour.overview.text}
            </p>
          </div>
          <div class="md:col-span-2 bg-slate-800/80 backdrop-blur-[10px] border border-white/10 p-6 rounded-xl shadow-2xl sticky top-24">
            <Headline tag="h3" class="text-2xl font-semibold mb-6 text-center text-primary">
              {tour.keyDetails.title}
            </Headline>
            <ul class="space-y-4">
              {tour.keyDetails.items.map((item: any) => (
                <li class="flex items-start text-md group hover:bg-slate-700/50 p-3 rounded-lg transition-all duration-300">
                  {item.icon && (
                    <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center mr-3 flex-shrink-0 mt-0.5 group-hover:bg-primary/30 transition-colors">
                      <Icon name={item.icon} class="w-5 h-5 text-primary" />
                    </div>
                  )}
                  <div>
                    <span class="font-semibold text-slate-200 block">{item.label}</span>
                    <p class="text-slate-300 text-sm">{item.value}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      <!-- Detailed Itinerary Section - Vertical Timeline -->
      <section>
        <Headline tag="h2" class="text-3xl md:text-4xl font-bold mb-10 text-center text-primary">
          {tour.itinerary.title}
        </Headline>
        <div class="relative max-w-3xl mx-auto">
          <div class="absolute left-1/2 top-0 bottom-0 w-1 bg-slate-700 transform -translate-x-1/2 hidden md:block"></div>
          {tour.itinerary.timeline.map((item: any, index: number) => {
            const iconContainerClasses = ["hidden", "md:flex", "flex-col", "items-center", "mr-8"];
            if (index % 2 === 0) {
              iconContainerClasses.push("md:order-1");
            } else {
              iconContainerClasses.push("md:order-3");
            }

            const contentBoxClasses = ["bg-slate-800", "p-6", "rounded-lg", "shadow-xl", "w-full", "md:w-[calc(50%-4rem)]", "transition-all", "duration-300", "ease-in-out", "hover:-translate-y-1", "hover:shadow-[0_10px_30px_rgba(0,0,0,0.3)]"];
            if (index % 2 === 0) {
              contentBoxClasses.push("md:order-2", "md:ml-auto");
            } else {
              contentBoxClasses.push("md:order-2", "md:mr-auto");
            }

            return (
            <div class="timeline-item mb-8 md:mb-12 flex md:items-center w-full transition-all duration-700 ease-out opacity-0 translate-y-[20px]">
              <div class:list={iconContainerClasses}>
                <div class="w-12 h-12 rounded-full flex items-center justify-center text-white mb-1 shadow-md bg-primary transition-all duration-300 ease-in-out hover:scale-110 hover:shadow-[0_8px_25px_rgba(30,64,175,0.3)]">
                  {item.icon && <Icon name={item.icon} class="w-6 h-6" />}
                </div>
                <div class="font-bold text-primary text-sm">{item.time}</div>
              </div>
              <div class:list={contentBoxClasses}>
                <div class="flex items-center mb-3 md:hidden">
                   <div class="w-10 h-10 rounded-full flex items-center justify-center text-white mr-3 shadow-md bg-primary transition-all duration-300 ease-in-out hover:scale-110 hover:shadow-[0_8px_25px_rgba(30,64,175,0.3)]">
                    {item.icon && <Icon name={item.icon} class="w-5 h-5" />}
                  </div>
                  <div class="font-bold text-primary text-md">{item.time}</div>
                </div>
                <h3 class="text-xl font-semibold mb-2 text-slate-100">{item.activity}</h3>
                {item.description && <p class="text-slate-300 text-sm leading-relaxed">{item.description}</p>}
              </div>
            </div>
            );
          })}
        </div>
      </section>

      <!-- What's Included Section -->
      <section>
        <div class="max-w-3xl mx-auto">
          <Headline tag="h2" class="text-3xl md:text-4xl font-bold mb-8 text-center text-primary">
            {tour.whatsIncluded.title}
          </Headline>
          <div class="bg-slate-800/80 backdrop-filter backdrop-blur-sm p-6 md:p-8 rounded-xl shadow-2xl border border-slate-700/50">
            <ul class="space-y-4 text-lg text-slate-300 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-4">
              {tour.whatsIncluded.items.map((item: any) => (
                <li class="flex items-center group hover:bg-slate-700/30 p-3 rounded-lg transition-all duration-300">
                  {item.icon && (
                    <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center mr-4 flex-shrink-0 group-hover:bg-primary/30 transition-colors">
                      <Icon name={item.icon} class="w-5 h-5 text-primary" />
                    </div>
                  )}
                  <span class="leading-relaxed">{item.text}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      <!-- Image Gallery -->
      <section>
        <Headline tag="h2" class="text-3xl md:text-4xl font-bold mb-8 text-center text-primary">
          {tour.imageGallery.title}
        </Headline>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {tour.imageGallery.images.map((image, index) => (
            <a href={image.src} data-pswp-width="1200" data-pswp-height="900" target="_blank" class="group block rounded-xl overflow-hidden shadow-lg relative transition-all duration-300 ease-in-out hover:-translate-y-1 hover:shadow-[0_15px_35px_rgba(0,0,0,0.4)] before:content-[''] before:absolute before:inset-0 before:bg-gradient-to-br before:from-blue-600/10 before:to-purple-600/10 before:opacity-0 before:transition-opacity before:duration-300 before:z-[1] hover:before:opacity-100">
              <Image
                src={image.src}
                alt={image.alt}
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                widths={[300, 600, 900]}
                sizes="(max-width: 639px) 100vw, (max-width: 1023px) 50vw, 33vw"
                aspectRatio="4:3"
                width={400}
                loading="lazy"
                decoding="async"
              />
              {image.caption && (
                <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/90 via-black/50 to-transparent transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 z-10">
                  <p class="text-white text-sm font-semibold text-shadow-lg">{image.caption}</p>
                </div>
              )}
              <div class="absolute top-3 right-3 w-8 h-8 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                <Icon name="tabler:zoom-in" class="w-4 h-4 text-white" />
              </div>
            </a>
          ))}
        </div>
      </section>

      <!-- Call to Action (Bottom of Page) -->
      <section class="text-center pt-10 pb-4">
         <div class="max-w-md mx-auto bg-slate-800 p-6 md:p-8 rounded-lg shadow-xl">
          <Headline tag="h3" class="text-2xl font-semibold mb-6 text-primary">Ready for an Unforgettable Adventure?</Headline>
          <div class="space-y-4">
            <Button variant="primary" size="xl" href={tour.ctaBottom.bookNow.href} target="_blank" rel="noopener noreferrer" class="w-full transform hover:scale-105 transition-transform duration-200">
              {tour.ctaBottom.bookNow.text}
            </Button>
            <Button variant="secondary" size="lg" href={tour.ctaBottom.callForQuote.href} class="w-full">
              {tour.ctaBottom.callForQuote.text}
            </Button>
          </div>
        </div>
      </section>
    </div>
  </div>

  <!-- Floating CTA Button -->
  <div id="floating-cta" class="fixed bottom-[10px] right-[10px] left-[10px] md:bottom-5 md:right-5 md:left-auto z-50 transition-all duration-300 ease-out opacity-0 translate-y-[100px]">
    <div class="bg-primary text-white px-4 py-2 rounded-full shadow-2xl flex items-center space-x-2 cursor-pointer hover:bg-primary/90 transition-colors">
      <Icon name="tabler:calendar" class="w-5 h-5" />
      <span class="hidden sm:inline font-semibold">Book Now</span>
      <span class="sm:hidden font-semibold">Book</span>
    </div>
  </div>

  <script>
    // Floating CTA functionality
    const floatingCta = document.getElementById('floating-cta');
    const heroSection = document.querySelector('[slot="hero"]');
    const ctaSection = document.querySelector('section:last-of-type');
    
    function toggleFloatingCta() {
      const heroRect = heroSection?.getBoundingClientRect();
      const ctaRect = ctaSection?.getBoundingClientRect();
      
      if (floatingCta && heroRect && ctaRect) {
        const heroVisible = heroRect.bottom > 0;
        const ctaVisible = ctaRect.top < window.innerHeight;
        
        if (!heroVisible && !ctaVisible) {
          floatingCta.classList.remove('opacity-0', 'translate-y-[100px]');
          floatingCta.classList.add('opacity-100', 'translate-y-0');
        } else {
          floatingCta.classList.remove('opacity-100', 'translate-y-0');
          floatingCta.classList.add('opacity-0', 'translate-y-[100px]');
        }
      }
    }
    
    window.addEventListener('scroll', toggleFloatingCta);
    
    floatingCta?.addEventListener('click', () => {
      window.open('https://book.mylimobiz.com/v4/radtransport', '_blank');
    });
    
    // Intersection Observer for timeline animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries, obs) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const element = entry.target as HTMLElement;
          element.classList.remove('opacity-0', 'translate-y-[20px]');
          element.classList.add('opacity-100', 'translate-y-0');
          obs.unobserve(element); // Stop observing after animation
        }
      });
    }, observerOptions);
    
    document.querySelectorAll('.timeline-item').forEach((item) => {
      observer.observe(item);
    });
    
    // Smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach((value: Element) => {
      const anchor = value as HTMLAnchorElement;
      anchor.addEventListener('click', function (this: HTMLAnchorElement, e: Event) {
        e.preventDefault();
        const hrefAttribute = this.getAttribute('href');
        
        if (hrefAttribute && hrefAttribute.trim() !== '' && hrefAttribute !== '#') {
          try {
            const targetElement = document.querySelector(hrefAttribute);
            if (targetElement) {
              targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
              });
            } else {
              console.warn(`Smooth scroll: Target element not found for selector '${hrefAttribute}'`);
            }
          } catch (error) {
            console.error(`Smooth scroll: Invalid selector '${hrefAttribute}'. Details:`, error);
          }
        } else if (hrefAttribute === '#') {
          console.warn('Smooth scroll: Link with href="#" was clicked. No specific target to scroll to, or default browser behavior prevented.');
        } else {
          console.warn('Smooth scroll: Anchor has no valid href attribute starting with #.');
        }
      });
    });
  </script>
</Layout>