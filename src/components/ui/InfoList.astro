---
interface Item {
  // Flexible item structure, adapt as needed
  description?: string;
  charge?: string;
  for?: string;
  notice?: string;
  [key: string]: any; // Allow other properties
}

interface Props {
  items: Item[];
  itemLayout?: 'row' | 'column'; // Optional: to control layout if needed
}

const { items, itemLayout = 'row' } = Astro.props;

// Helper to get displayable key-value pairs, excluding known structural keys
function getDisplayableProperties(item: Item) {
  const excludedKeys = ['description', 'charge', 'for', 'notice', 'icon']; // Add more if needed
  return Object.entries(item)
    .filter(([key]) => !excludedKeys.includes(key) && item[key] !== undefined && item[key] !== null)
    .map(([key, value]) => ({ key: formatKey(key), value }));
}

function formatKey(key: string) {
  return key
    .replace(/([A-Z])/g, ' $1') // Add space before uppercase letters
    .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
}
---

<ul class="space-y-4">
  {items.map((item) => (
    <li class="p-4 border border-gray-200 dark:border-slate-700 rounded-md shadow-sm hover:shadow-md transition-shadow duration-200">
      {item.description && item.charge && (
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <p class="text-gray-700 dark:text-slate-300 font-medium">{item.description}</p>
          <p class="text-gray-900 dark:text-white font-semibold mt-1 sm:mt-0">{item.charge}</p>
        </div>
      )}
      {item.for && item.notice && (
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <p class="text-gray-700 dark:text-slate-300 font-medium">{item.for}</p>
          <p class="text-gray-900 dark:text-white font-semibold mt-1 sm:mt-0">{item.notice}</p>
        </div>
      )}
      <!-- Fallback for other structures if primary ones are not met -->
      {!((item.description && item.charge) || (item.for && item.notice)) && getDisplayableProperties(item).map(prop => (
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <p class="text-gray-700 dark:text-slate-300 font-medium">{prop.key}</p>
          <p class="text-gray-900 dark:text-white font-semibold mt-1 sm:mt-0">{String(prop.value)}</p>
        </div>
      ))}
    </li>
  ))}
</ul>