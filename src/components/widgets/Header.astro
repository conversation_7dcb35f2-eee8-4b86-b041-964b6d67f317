---
import { Icon } from 'astro-icon/components';
import Logo from '~/components/Logo.astro';
import ToggleTheme from '~/components/common/ToggleTheme.astro';
import ToggleMenu from '~/components/common/ToggleMenu.astro';
import Button from '~/components/ui/Button.astro';

import { getHomePermalink } from '~/utils/permalinks';
import { trimSlash } from '~/utils/permalinks';
import type { CallToAction } from '~/types';

interface Link {
  text?: string;
  href?: string;
  ariaLabel?: string;
  icon?: string;
}

export interface ActionLink extends CallToAction {}

interface MenuLink extends Link {
  links?: Array<MenuLink>;
}

export interface Props {
  id?: string;
  links?: Array<MenuLink>;
  actions?: Array<ActionLink>;
  isSticky?: boolean;
  isDark?: boolean;
  isFullWidth?: boolean;
  showToggleTheme?: boolean;
  showRssFeed?: boolean;
  position?: string;
  isOverlay?: boolean;
}

const {
  id = 'header',
  links = [],
  actions = [],
  isSticky = true,
  isDark = false,
  isFullWidth = false,
  showToggleTheme = false,
  position = 'right',
  isOverlay = false,
} = Astro.props;

const currentPath = `/${trimSlash(new URL(Astro.url).pathname)}`;

// Detect if we're on the airport-transportation page
const isAirportPage = currentPath === '/airport-transportation';
const shouldOverlay = isOverlay || isAirportPage;

const hasMultipleWords = (text?: string): boolean => {
  return text ? text.trim().split(/\s+/).length > 1 : false;
};
---

<header
  class:list={[
    {
      sticky: isSticky && !shouldOverlay,
      relative: !isSticky && !shouldOverlay,
      fixed: shouldOverlay,
      dark: isDark,
      'top-0 z-50 left-0 right-0': shouldOverlay,
      'top-0 z-40 flex-none mx-auto w-full': !shouldOverlay
    },
    'transition-[opacity] ease-in-out',
  ]}
  {...(isSticky && !shouldOverlay) ? { 'data-aw-sticky-header': true } : {}}
  {...id ? { id } : {}}
>
  <div class="absolute inset-0 bg-black"></div>
  <div
    class:list={[
      'relative text-default py-[20px] px-3 lg:px-6 mx-auto w-full lg:flex lg:justify-between lg:items-center',
      {
        'max-w-7xl': !isFullWidth,
      },
    ]}
  >
    <div
      class:list={[
        { 'mr-auto rtl:mr-0 rtl:ml-auto': position === 'right' },
        'flex justify-between flex-shrink-0',
      ]}
    >
      <a
        class="flex items-center flex-shrink-0"
        href={getHomePermalink()}
      >
        <Logo />
      </a>
      <div class="flex items-center lg:hidden">
        <ToggleMenu />
      </div>
    </div>
    <nav
      class="items-center w-full lg:w-auto hidden lg:flex text-default lg:mx-5"
      aria-label="Main navigation"
    >
      <ul
        class="flex flex-col lg:flex-row lg:self-center lg:items-center w-full h-auto lg:h-full lg:w-auto lg:text-xs tracking-[1.3px] font-medium uppercase"
      >
        {
          links.map(({ text, href, links }) => (
            <li
              class:list={{
                dropdown: links?.length,
                'flex leading-6 h-full': !links?.length,
              }}
            >
              {links?.length ? (
                <>
                  <button class="hover:text-link dark:hover:text-white px-4 py-3 flex items-center">
                    {text}{' '}
                    <Icon
                      name="tabler:chevron-down"
                      class="w-3.5 h-3.5 ml-0.5 rtl:ml-0 rtl:mr-0.5 hidden lg:inline"
                    />
                  </button>
                  <ul class="dropdown-menu lg:backdrop-blur-lg dark:lg:bg-dark rounded lg:absolute pl-4 lg:pl-0 lg:hidden font-medium lg:bg-white/90 lg:min-w-[200px] drop-shadow-xl">
                    {links.map(({ text: text2, href: href2 }) => (
                      <li>
                        <a
                          class:list={[
                            'first:rounded-t tracking-[1.3px] last:rounded-b lg:hover:bg-gray-100 hover:text-link dark:hover:text-white dark:hover:black py-2 px-5 block whitespace-no-wrap',
                            { 'aw-link-active ': href2 === currentPath },
                          ]}
                          href={href2}
                        >
                          {text2}
                        </a>
                      </li>
                    ))}
                  </ul>
                </>
              ) : (
                <a
                  class:list={[
                    'text-white hover:text-link font-medium dark:hover:text-slate-600 px-4 h-full flex items-center leading-3 border-[#59c0ff] hover:border-b',
                    {
                      'aw-link-active': href === currentPath,
                      'w-min': hasMultipleWords(text),
                    },
                  ]}
                  href={href}
                >
                  {text}
                </a>
              )}
            </li>
          ))
        }
      </ul>
    </nav>
    <div
      class:list={[
        { 'ml-auto rtl:ml-0 rtl:mr-auto': position === 'left' },
        'hidden lg:self-center lg:flex items-center lg:mb-0 fixed w-full lg:w-auto lg:static justify-end left-0 rtl:left-auto rtl:right-0 bottom-0 p-3 lg:p-0 flex-shrink-0',
      ]}
    >
      <div class="items-center flex justify-center w-full lg:w-auto">
        <div class="flex">
          {
            showToggleTheme && (
              <ToggleTheme iconClass="w-6 h-6 lg:w-5 lg:h-5 lg:inline-block" />
            )
          }
          <!-- {
            showRssFeed && (
              <a
                class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
                aria-label="RSS Feed"
                href={getAsset('/rss.xml')}
              >
                <Icon name="tabler:rss" class="w-5 h-5" />
              </a>
            )
          } -->
        </div>
        {
          actions?.length ? (
            <span class="ml-4 rtl:ml-0 rtl:mr-4 justify-center flex gap-x-3">
              {actions.map((action) => {
                let modifiedAction = { ...action };
                if (
                  action.text === 'Book Today' &&
                  currentPath === '/airport-transportation'
                ) {
                  modifiedAction.href = '#booking_section';
                }
                return (
                  <Button
                    {...modifiedAction}
                    class="w-auto uppercase"
                  />
                );
              })}
            </span>
          ) : (
            ''
          )
        }
      </div>
    </div>
  </div>
</header>
