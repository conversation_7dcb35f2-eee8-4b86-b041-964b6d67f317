// eslint-disable-next-line @typescript-eslint/triple-slash-reference
/// <reference path="../.astro/types.d.ts" />
/// <reference types="tailwindcss-intersect" />
declare module 'tailwindcss-intersect';
/// <reference types="astro/client" />
/// <reference types="vite/client" />

// Extend JSX to allow custom attributes for external scripts
declare namespace astroHTML.JSX {
  interface ScriptHTMLAttributes {
    config?: string;
  }
}
