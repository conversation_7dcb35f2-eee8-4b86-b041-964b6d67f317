---
import '~/assets/styles/tailwind.css';

import { I18N } from '~/utils/config';
import SpeedInsights from '@vercel/speed-insights/astro';
import CommonMeta from '~/components/common/CommonMeta.astro';
import Favicons from '~/components/Favicons.astro';
import CustomStyles from '~/components/CustomStyles.astro';
import ApplyColorMode from '~/components/common/ApplyColorMode.astro';
import Metadata from '~/components/common/Metadata.astro';
import SiteVerification from '~/components/common/SiteVerification.astro';
import Analytics from '~/components/common/Analytics.astro';
import BasicScripts from '~/components/common/BasicScripts.astro';
import { ClientRouter } from 'astro:transitions';
// Import ClientRouter from astro:transitions

import type { MetaData as MetaDataType } from '~/types';

export interface Props {
  metadata?: MetaDataType;
}

const { metadata = {} } = Astro.props;
const { language, textDirection } = I18N;
---

<!doctype html>
<html
  lang={language}
  dir={textDirection}
  class="3xl:text-[20px]"
>
  <head>
    <CommonMeta />
    <Favicons />
    <CustomStyles />
    <ApplyColorMode />
    <Metadata {...metadata} />
    <SiteVerification />
    <Analytics />
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/iframe-resizer/4.3.9/iframeResizer.min.js"
      integrity="sha512-+bpyZqiNr/4QlUd6YnrAeLXzgooA1HKN5yUagHgPSMACPZgj8bkpCyZezPtDy5XbviRm4w8Z1RhfuWyoWaeCyg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
      is:inline
    ></script>
  </head>

  <body
    class="antialiased text-default text-xs bg-page tracking-[1.3px] overflow-x-hidden"
  >
    <slot />

    <BasicScripts />

    <style is:global>
      img {
        content-visibility: auto;
      }
    </style>
    <SpeedInsights />
  </body>
</html>
