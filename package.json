{"name": "radtransport", "description": "Rad Transport", "version": "1.0.0-beta.12", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro", "format": "prettier -w \"**/*.{js,ts,astro}\"", "lint:eslint": "eslint .", "lint": "bunx --bun eslint"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/rss": "^4.0.10", "@astrojs/sitemap": "^3.2.1", "@astrojs/ts-plugin": "^1.10.4", "@astrolib/analytics": "^0.6.1", "@astrolib/seo": "^1.0.0-beta.8", "@eslint/js": "^9.17.0", "@fontsource-variable/inter": "^5.1.0", "@fontsource-variable/raleway": "^5.1.0", "@types/js-yaml": "^4.0.9", "@unpic/astro": "^0.1.0", "@vercel/speed-insights": "^1.1.0", "astro": "^5.1.1", "astro-icon": "^1.1.5", "iframe-resizer": "5.3.2", "limax": "4.1.0", "lodash.merge": "^4.6.2", "sharp": "^0.33.5", "tailwindcss-animated": "^1.1.2", "typescript-esbuild": "^0.4.10", "typescript-eslint": "^8.18.2", "unpic": "^3.22.0"}, "devDependencies": {"@astrojs/mdx": "^4.0.3", "@astrojs/partytown": "^2.1.2", "@astrojs/tailwind": "^5.1.4", "@iconify-json/flat-color-icons": "^1.2.1", "@iconify-json/tabler": "^1.2.13", "@tailwindcss/typography": "^0.5.15", "@types/lodash.merge": "^4.6.9", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "js-yaml": "^4.1.0", "mdast-util-to-string": "^4.0.0", "prettier": "^3.4.2", "prettier-plugin-astro": "^0.14.1", "reading-time": "^1.5.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-intersect": "^2.0.1", "typescript": "^5.7.2"}, "engines": {"node": ">=20.0.0"}, "trustedDependencies": ["@parcel/watcher", "@vercel/speed-insights", "esbuild", "sharp"]}