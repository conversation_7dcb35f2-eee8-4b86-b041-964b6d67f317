{
  "extends": "astro/tsconfigs/strict",
   "include": [".astro/types.d.ts", "src/**/*"],
  "exclude": ["dist"],
  "compilerOptions": {
    "plugins": [
      {
        "name": "@astrojs/ts-plugin"
      },
    ],
    "strict": true,
    "moduleResolution": "bundler",
    "allowJs": true,
    "baseUrl": ".",
    "paths": {
      "~/*": ["src/*"]
    },
    "typeRoots": ["./node_modules/@types", "./src/types"]
  }
}
